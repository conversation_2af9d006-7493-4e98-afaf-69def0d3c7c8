# TaskManager MCP 使用指南

> 版本：1.0.0  
> 更新日期：2025-01-02  

## 目录

- [简介](#简介)
- [功能特点](#功能特点)
- [安装配置](#安装配置)
- [使用方法](#使用方法)
- [工具说明](#工具说明)
- [常见问题](#常见问题)

## 简介

TaskManager MCP 是一个基于模型上下文协议(MCP)的任务管理服务，允许 Claude Desktop (或任何 MCP 客户端) 在基于队列的系统中管理和执行任务。

## 功能特点

- **任务规划**：注册用户请求并规划相关任务
- **任务队列管理**：基于队列的任务执行系统
- **进度跟踪**：实时显示任务进度表
- **用户审批流程**：每个任务完成后需要用户确认
- **请求完成确认**：所有任务完成后需要最终确认

## 安装配置

### 前置要求

- Node.js (v14.0.0或更高版本)
- npm 包管理器
- Cursor IDE (支持MCP的编辑器)
- Smithery 账户和有效的 API 密钥

### 配置步骤

#### 1. 项目级配置

在项目根目录创建 `.cursor` 文件夹，并在其中创建 `mcp.json` 文件：

```json
{
  "mcpServers": {
    "taskmanager": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@smithery/cli@latest",
        "run",
        "@kazuph/mcp-taskmanager",
        "--profile",
        "your-profile-name",
        "--key",
        "your-api-key"
      ]
    }
  }
}
```

#### 2. 重启 Cursor

配置完成后，重启 Cursor IDE 以加载新的 MCP 配置。

#### 3. 验证配置

在 Cursor 中打开 Settings > MCP，检查 taskmanager 服务是否正常启动。

## 使用方法

### 基本工作流程

TaskManager 遵循严格的工作流程：

1. **请求规划** (`request_planning`)：注册新的用户请求并规划相关任务
2. **获取下一个任务** (`get_next_task`)：检索下一个未完成的任务
3. **标记任务完成** (`mark_task_done`)：完成任务后标记为已完成
4. **任务完成审批** (`approve_task_completion`)：用户确认任务完成
5. **重复步骤2-4**：直到所有任务完成
6. **请求完成审批** (`approve_request_completion`)：用户确认整个请求完成

### 重要注意事项

- **必须等待用户审批**：每个任务完成后，必须等待用户使用 `approve_task_completion` 确认
- **不能自动进行下一个任务**：在用户审批之前，不能调用 `get_next_task`
- **进度表显示**：每次操作后都会显示当前所有任务的进度表

## 工具说明

### 1. request_planning
注册新的用户请求并规划相关任务。

**参数：**
- `originalRequest` (必需)：原始用户请求
- `tasks` (必需)：任务列表
- `splitDetails` (可选)：任务拆分详情

### 2. get_next_task
获取下一个待处理的任务。

**参数：**
- `requestId` (必需)：请求ID

**返回：**
- 下一个待处理任务的详情
- 当前所有任务的进度表

### 3. mark_task_done
标记任务为已完成。

**参数：**
- `requestId` (必需)：请求ID
- `taskId` (必需)：任务ID
- `completedDetails` (可选)：完成详情

### 4. approve_task_completion
用户确认任务完成。

**注意：** 这个工具必须由用户调用，AI助手不能自动调用。

### 5. approve_request_completion
用户确认整个请求完成。

**注意：** 这个工具必须由用户调用，AI助手不能自动调用。

## 常见问题

### Q: 为什么出现 MaxListenersExceededWarning 错误？

**A:** 这通常是因为直接运行 smithery CLI 而不是通过 MCP 配置。请确保：
1. 使用 `.cursor/mcp.json` 配置文件
2. 重启 Cursor IDE
3. 不要直接运行 `npx @smithery/cli` 命令

### Q: 任务管理器无法启动怎么办？

**A:** 检查以下几点：
1. 确认 API 密钥和 profile 名称正确
2. 检查网络连接
3. 确认 Node.js 版本兼容
4. 查看 Cursor 的 MCP 日志

### Q: 如何获取 Smithery API 密钥？

**A:** 
1. 访问 [Smithery.ai](https://smithery.ai)
2. 注册账户
3. 在个人资料中生成 API 密钥

### Q: 可以同时运行多个任务吗？

**A:** TaskManager 设计为顺序执行任务，每次只能处理一个任务，确保用户对每个步骤都有控制权。

## 参考资源

- [TaskManager GitHub 仓库](https://github.com/kazuph/mcp-taskmanager)
- [Smithery 官方网站](https://smithery.ai)
- [Model Context Protocol 规范](https://github.com/anthropics/anthropic-cookbook/tree/main/mcp)
- [Cursor IDE 官方文档](https://cursor.sh/docs)