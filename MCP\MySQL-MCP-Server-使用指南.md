# MySQL MCP Server 使用指南

> 版本：1.0.0  
> 更新日期：2025-04-05  

## 目录

- [简介](#简介)
- [前置要求](#前置要求)
- [安装方法](#安装方法)
  - [全局安装](#全局安装)
  - [项目级安装](#项目级安装)
- [配置说明](#配置说明)
  - [全局配置](#全局配置)
  - [项目级配置](#项目级配置)
  - [配置参数说明](#配置参数说明)
- [使用方法](#使用方法)
  - [基本SQL查询](#基本sql查询)
  - [常用操作示例](#常用操作示例)
- [故障排除](#故障排除)
- [安全建议](#安全建议)
- [参考资源](#参考资源)

## 简介

MySQL MCP Server 是一个基于模型上下文协议(MCP)的服务，允许AI助手（如Claude）安全地与MySQL数据库进行交互。通过该服务，AI助手可以列出数据库表、读取表结构、执行SQL查询等，大大增强了AI助手在数据分析和数据库操作方面的能力。

MCP (Model Context Protocol) 是一种允许AI模型与外部工具和服务安全交互的协议，它提供了一个标准化的接口，使AI模型能够执行特定任务，如数据库查询、文件操作等。

## 前置要求

使用MySQL MCP Server需要满足以下条件：

- Node.js (v14.0.0或更高版本)
- npm或yarn包管理器
- Cursor IDE (支持MCP的编辑器)
- MySQL数据库服务器
- 数据库连接信息（主机、端口、用户名、密码、数据库名）

## 安装方法

### 全局安装

全局安装允许你在任何Cursor项目中使用MySQL MCP Server。

1. 打开命令行终端
2. 运行以下命令安装Smithery CLI工具：
   ```bash
   npm install -g @smithery/cli
   ```

3. 安装mysql-mcp-server：
   ```bash
   npm install -g mysql-mcp-server
   ```

### 项目级安装

如果你只想在特定项目中使用MySQL MCP Server，可以使用以下命令：

```bash
npx @smithery/cli install mysql-mcp-server --client cursor
```

此命令会提示你输入MySQL连接信息，并自动配置项目的MCP设置。

## 配置说明

### 全局配置

全局配置文件位于：`C:\Users\<USER>\.cursor\mcp.json`

创建或编辑此文件，添加以下内容：

```json
{
  "mcpServers": {
    "mysql-mcp-server": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "mysql-mcp-server"
      ],
      "env": {
        "MYSQL_HOST": "你的数据库主机",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "你的用户名",
        "MYSQL_PASSWORD": "你的密码",
        "MYSQL_DATABASE": "你的数据库名"
      }
    }
  }
}
```

### 项目级配置

在项目根目录创建`.cursor`文件夹，并在其中创建`mcp.json`文件：

```json
{
  "mcpServers": {
    "mysql-mcp-server": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "mysql-mcp-server"
      ],
      "env": {
        "MYSQL_HOST": "你的数据库主机",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "你的用户名",
        "MYSQL_PASSWORD": "你的密码",
        "MYSQL_DATABASE": "你的数据库名"
      }
    }
  }
}
```

### 配置参数说明

- **command**: 在Windows环境下使用`cmd`，在macOS/Linux环境下使用`bash`或其他shell
- **args**: 命令行参数，启动mysql-mcp-server
- **env**: 环境变量，包括MySQL连接信息
  - **MYSQL_HOST**: MySQL服务器主机名或IP地址
  - **MYSQL_PORT**: MySQL服务器端口号（默认3306）
  - **MYSQL_USER**: 数据库用户名
  - **MYSQL_PASSWORD**: 数据库密码
  - **MYSQL_DATABASE**: 要连接的数据库名

## 使用方法

配置完成后，在Cursor中重启应用程序，然后打开Settings > MCP查看mysql-mcp-server是否正常启动。服务启动后，可以通过与Claude对话的方式执行数据库查询。

### 基本SQL查询

你可以直接要求Claude执行SQL查询，例如：

- "请查询users表中有多少条数据"
- "获取products表的前10条记录"
- "列出所有数据表"

### 常用操作示例

1. **列出所有数据库**
   ```
   请列出MySQL服务器上的所有数据库
   ```

2. **列出数据库中的所有表**
   ```
   请列出当前数据库中的所有表
   ```

3. **查看表结构**
   ```
   请描述users表的结构
   ```

4. **执行简单查询**
   ```
   请执行SELECT COUNT(*) FROM users
   ```

5. **复杂查询示例**
   ```
   请查询近30天内注册的用户数量，按日期分组
   ```

## 故障排除

### 常见问题及解决方案

1. **无法启动MCP服务**
   - 检查Node.js和npm是否正确安装
   - 确认全局或项目级mcp.json文件格式正确
   - 在终端中手动执行命令进行测试

2. **数据库连接失败**
   - 验证数据库主机IP是否可以ping通
   - 确认数据库用户名和密码正确
   - 检查数据库服务器是否允许远程连接
   - 确认防火墙未阻止数据库连接

3. **JSON语法错误**
   - 检查mcp.json文件的语法，确保括号匹配
   - 删除多余的逗号
   - 使用在线JSON验证工具验证格式

4. **Windows系统特定问题**
   - 确保使用"cmd"作为命令执行器
   - 使用"/c"参数执行命令
   - 避免路径中使用反斜杠(\)，改用正斜杠(/)

## 安全建议

为了保护数据库安全，建议采取以下措施：

1. **创建只读用户**：为MCP服务创建一个只有SELECT权限的专用数据库用户
2. **避免使用root账户**：永远不要使用root或管理员账户进行连接
3. **限制表访问**：只授权访问必要的数据表
4. **定期更改密码**：定期更新数据库密码
5. **加密配置**：考虑对包含密码的配置文件进行加密

## 参考资源

- [MySQL MCP Server GitHub仓库](https://github.com/designcomputer/mysql_mcp_server)
- [Smithery MCP服务器列表](https://smithery.ai/server/mysql-mcp-server)
- [Model Context Protocol (MCP) 规范](https://github.com/anthropics/anthropic-cookbook/tree/main/mcp)
- [Cursor IDE官方文档](https://cursor.sh/docs) 