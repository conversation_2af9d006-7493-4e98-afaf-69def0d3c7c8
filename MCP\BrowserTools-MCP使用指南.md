# BrowserTools MCP 使用指南

> 作者：医院护理管理系统开发团队  
> 更新日期：2025-03-14  
> 版本：1.0.0

## 目录

- [简介](#简介)
- [安装配置](#安装配置)
  - [前置要求](#前置要求)
  - [安装流程](#安装流程)
  - [配置说明](#配置说明)
- [使用指南](#使用指南)
  - [基本功能](#基本功能)
  - [截图功能](#截图功能)
  - [调试功能](#调试功能)
  - [审计功能](#审计功能)
- [常见问题](#常见问题)
- [注意事项](#注意事项)
- [最佳实践](#最佳实践)

## 简介

BrowserTools MCP 是一套强大的浏览器调试工具，专为与 AI 代码编辑器（如 Cursor）配合使用而设计。它允许 AI 助手直接访问浏览器状态并与之交互，大大提升了前端调试和开发效率。

主要功能包括：
- 访问控制台日志和错误
- 监控网络请求和响应
- 截取浏览器页面截图
- 查看选中的 DOM 元素
- 进行 SEO、性能和代码质量扫描
- 专门针对 NextJS 应用的 SEO 审计
- 调试模式和审计模式

## 安装配置

### 前置要求

确保你的开发环境满足以下要求：
- NodeJS 已安装
- Google Chrome 或基于 Chromium 的浏览器
- MCP 客户端应用（如 Cursor、Windsurf、RooCode 等）
- Windows 10 或更高版本

> **注意**：Model Context Protocol (MCP) 专用于 Anthropic 模型。使用 Cursor 时，请确保启用了 Claude 3.5 Sonnet 作为模型的编辑器代理。

### 安装流程

#### 1. 安装 Chrome 扩展

- 官方 Chrome 扩展正在等待 Google 商店审批
- 临时解决方案：从 GitHub 下载
  ```bash
  git clone https://github.com/AgentDeskAI/browser-tools-mcp.git
  ```
- 打开 Chrome 浏览器的"管理扩展程序"窗口
- 启用"开发者模式"
- 点击"加载已解压的扩展程序"
- 导航到下载的 Chrome 扩展目录，确保已解压，点击"选择"
- 确认扩展列表中显示 **BrowserToolsMCP**

#### 2. 配置 Cursor MCP 服务器

- 打开 Cursor 设置
- 进入"功能"(Features)部分，滚动到"MCP 服务器"部分
- 点击"添加新 MCP 服务器"
- 设置以下参数：
  - 名称：`browser-tools`
  - 类型：`command`
  - 命令配置：
    ```json
    {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@agentdeskai/browser-tools-mcp@1.2.0"
      ]
    }
    ```

#### 3. 创建 MCP 配置文件

在项目根目录创建或修改 `.cursor/mcp.json` 文件：

```json
{
  "mcpServers": {
    "browser-tools": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@agentdeskai/browser-tools-mcp@1.2.0"
      ],
      "enabled": true
    }
  }
}
```

#### 4. 运行 BrowserTools 服务器

打开新的终端窗口，运行以下命令启动服务器：

```bash
npx @agentdeskai/browser-tools-server@1.2.0
```

服务器将在端口 3025 上运行，确保该端口没有被其他进程占用。

#### 5. 打开 Chrome 开发者控制台

- 在任意网页上右键点击，选择"检查"打开 Chrome 开发者工具
- 日志将自动被捕获并可被 MCP 客户端访问

### 配置说明

#### 截图保存位置配置

默认情况下，截图会保存到 `Downloads/mcp-screenshots` 目录。如需自定义保存位置：

1. 打开 Chrome 开发者工具
2. 切换到 BrowserToolsMCP 面板（如果看不到，点击 `>>` 查看更多标签）
3. 找到 "Screenshot Directory" 输入框
4. 输入自定义路径，例如：`D:\Jolly\Develop\gitee\nursing-system\mcp-screenshots`
5. 按回车确认

## 使用指南

### 基本功能

#### 查看控制台日志

在 Cursor 中向 AI 提问：
```
查看浏览器控制台日志
```

AI 将使用 MCP 工具获取并显示当前控制台日志。

#### 查看控制台错误

在 Cursor 中向 AI 提问：
```
检查浏览器控制台错误
```

AI 将获取并分析控制台错误信息。

#### 查看网络请求

在 Cursor 中向 AI 提问：
```
查看网络请求日志
```

AI 将获取并显示网络请求信息。

### 截图功能

在 Cursor 中向 AI 提问：
```
请帮我截取当前浏览器页面的截图
```

AI 将捕获浏览器当前页面的截图并保存到配置的目录中。

### 调试功能

#### 进入调试模式

在 Cursor 中向 AI 提问：
```
这个功能不工作，请进入调试模式帮我检查问题
```

AI 将启动调试模式，综合使用多种工具帮助诊断问题。

#### 检查选中元素

1. 在浏览器中选择一个 HTML 元素（使用开发者工具的选择器）
2. 在 Cursor 中向 AI 提问：
   ```
   请帮我检查当前选中的元素
   ```

AI 将获取并分析当前选中的 DOM 元素。

### 审计功能

#### 性能审计

在 Cursor 中向 AI 提问：
```
请对当前网页进行性能审计
```

AI 将运行性能审计并提供优化建议。

#### SEO 审计

在 Cursor 中向 AI 提问：
```
请对当前网页进行 SEO 审计
```

AI 将运行 SEO 审计并提供优化建议。

## 常见问题

### 截图工具失败

**问题**：使用截图功能时失败。

**解决方案**：
- 确保使用了正确版本的 MCP 服务器：`@agentdeskai/browser-tools-mcp@1.2.0`
- 检查浏览器扩展是否正确安装
- 确保服务器正在运行
- 重新启动服务器和浏览器

### 看不到截图

**问题**：无法找到保存的截图。

**解决方案**：
- 检查默认保存位置：`Downloads/mcp-screenshots`
- 确认是否已设置自定义路径
- 确保路径存在且有写入权限
- 在 BrowserToolsMCP 面板中检查配置

### 看不到任何日志

**问题**：无法获取浏览器日志。

**解决方案**：
- 确保在想要捕获日志的浏览器标签中打开了开发者工具
- 检查 BrowserTools 服务器是否正在运行
- 在当前页面产生一些控制台日志进行测试
- 重新刷新页面并重新打开开发者工具

### 日志过多

**问题**：看到太多不相关的日志。

**解决方案**：
- 关闭其他标签页中打开的开发者工具
- BrowserToolsMCP 会捕获多个标签页的日志
- 使用 BrowserToolsMCP 面板中的"清除日志"按钮

### 日志消失

**问题**：之前的日志突然消失。

**解决方案**：
- 每次刷新页面或重启服务器时，日志会被清除
- 如需保留日志，请在清除前将其复制保存

## 注意事项

1. **服务器端口**：
   - BrowserTools 服务器默认在端口 3025 上运行
   - 确保该端口未被其他程序占用
   - 如果发生端口冲突，需手动终止占用进程或更改配置

2. **Chrome 扩展权限**：
   - 确保给予扩展足够的权限访问网页内容
   - 某些网站可能限制扩展功能

3. **文件路径处理**：
   - Windows 路径使用反斜杠（`\`）或正斜杠（`/`）
   - 包含空格的路径需要用引号包裹
   - 避免使用特殊字符

4. **包版本控制**：
   - 明确指定版本号：`@1.2.0`
   - 避免使用最新版（`latest`），以防不兼容更新

5. **资源消耗**：
   - 日志记录和截图功能会消耗内存
   - 定期清理日志以防内存泄漏

6. **安全考虑**：
   - BrowserTools 可以访问您的浏览内容
   - 不要在包含敏感信息的页面上使用
   - 仅在开发环境中使用

## 最佳实践

1. **开发工作流程**：
   - 在开始开发会话前启动 BrowserTools 服务器
   - 使用专门的浏览器窗口进行开发，减少干扰
   - 定期清理日志以保持性能

2. **截图管理**：
   - 为项目创建专用的截图文件夹
   - 使用有意义的命名约定
   - 定期清理不需要的截图

3. **调试效率**：
   - 使用清晰具体的查询与 AI 交流
   - 提供足够的上下文信息
   - 针对特定问题使用特定工具

4. **团队使用**：
   - 在团队中统一配置和路径约定
   - 共享常用问题解决方案
   - 保持工具版本一致性

5. **学习资源**：
   - 关注 BrowserTools 的官方更新：https://github.com/AgentDeskAI/browser-tools-mcp
   - 参考 Cursor 的 MCP 集成文档
   - 实验不同的查询方式以了解工具能力

---

**祝您使用愉快！如有任何问题，请联系开发团队。** 