# Shrimp Task Manager 配置指南

## 问题解决

你之前遇到的 `MaxListenersExceededWarning` 错误是因为使用了错误的包名和配置方式。

## 正确的配置

### 在 Augment Code 插件中配置

请在你的 Augment Code 插件中使用以下 JSON 配置：

```json
{
  "mcpServers": {
    "mcp-shrimp-task-manager": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "mcp-shrimp-task-manager"
      ],
      "env": {
        "DATA_DIR": "D:\\Jolly\\Develop\\Cursor-rules\\shrimp-data",
        "TEMPLATES_USE": "en",
        "ENABLE_GUI": "false"
      }
    }
  }
}
```

### 关键修正点

1. **包名修正**：从 `@cjo4m06/mcp-shrimp-task-manager` 改为 `mcp-shrimp-task-manager`
2. **移除 smithery CLI**：不再使用 `@smithery/cli@latest run`
3. **移除认证参数**：不需要 `--key` 和 `--profile` 参数
4. **添加环境变量**：配置数据目录和模板语言

### 配置说明

- **DATA_DIR**: 任务数据存储目录，建议使用绝对路径
- **TEMPLATES_USE**: 模板语言，`en` 为英文，`zh` 为中文
- **ENABLE_GUI**: 是否启用 Web 界面，设为 `false` 禁用

## 验证配置

配置完成后：

1. 保存配置并重启 Augment Code 插件
2. 检查 MCP 服务列表中是否显示 `mcp-shrimp-task-manager`
3. 状态应该显示为绿色（运行中）
4. 工具数量应该显示为 15 个

## 功能特点

Shrimp Task Manager 提供以下核心功能：

- **plan_task**: 任务规划
- **analyze_task**: 任务分析  
- **reflect_task**: 反思改进
- **split_tasks**: 任务分解
- **execute_task**: 任务执行
- **list_tasks**: 任务列表
- **research_mode**: 研究模式
- **init_project_rules**: 项目规则初始化

## 使用方法

配置成功后，你可以通过以下命令使用：

- `plan task [描述]` - 规划任务
- `execute task [任务名]` - 执行任务
- `list tasks` - 查看任务列表
- `research [主题]` - 进入研究模式
- `init project rules` - 初始化项目规则

## 优势对比

| 特性 | @kazuph/mcp-taskmanager | mcp-shrimp-task-manager |
|------|-------------------------|-------------------------|
| 工具数量 | 10个 | 15个 |
| 运行方式 | 需要 smithery CLI | 本地直接运行 |
| 稳定性 | 有内存泄漏问题 | 更稳定 |
| 功能特点 | 基础任务管理 | 专为编程设计 |
| 配置复杂度 | 需要 API 密钥 | 简单配置 |