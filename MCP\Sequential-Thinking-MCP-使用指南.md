# Sequential Thinking MCP 使用指南

> 版本：1.0.0  
> 更新日期：2025-04-05  

## 目录

- [简介](#简介)
- [功能特点](#功能特点)
- [安装配置](#安装配置)
  - [前置要求](#前置要求)
  - [安装步骤](#安装步骤)
  - [配置说明](#配置说明)
- [使用方法](#使用方法)
  - [基本概念](#基本概念)
  - [使用场景](#使用场景)
  - [常用案例](#常用案例)
- [参数说明](#参数说明)
- [常见问题](#常见问题)
- [参考资源](#参考资源)

## 简介

Sequential Thinking MCP 是一个基于模型上下文协议(MCP)的服务，旨在帮助AI助手(如Claude)进行结构化、分步骤的思考过程。它允许AI将复杂问题分解为可管理的步骤，在理解深入时修订和完善思路，并探索替代推理路径。

## 功能特点

- **分步思考**：将复杂问题分解为可管理的步骤
- **思路修订**：随着理解的深入修改和完善之前的想法
- **思考分支**：在不同的推理路径上展开探索
- **动态调整**：根据需要调整思考步骤的总数
- **解决方案验证**：生成和验证解决方案假设

## 安装配置

### 前置要求

使用Sequential Thinking MCP需要满足以下条件：

- Node.js (v14.0.0或更高版本)
- npm或yarn包管理器
- Cursor IDE (支持MCP的编辑器)

### 安装步骤

#### 方法一：使用npm全局安装

```bash
npm install -g @modelcontextprotocol/server-sequential-thinking
```

#### 方法二：使用npx运行

不需要预先安装，直接在使用时通过npx运行：

```bash
npx @modelcontextprotocol/server-sequential-thinking
```

### 配置说明

#### 项目级配置

在项目根目录创建`.cursor`文件夹，并在其中创建`mcp.json`文件：

```json
{
  "mcpServers": {
    "sequential-thinking": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    }
  }
}
```

#### 全局配置

编辑用户目录下的`.cursor/mcp.json`文件：

```json
{
  "mcpServers": {
    "sequential-thinking": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    }
  }
}
```

## 使用方法

配置完成后，在Cursor中重启应用程序，然后打开Settings > MCP查看sequential-thinking是否正常启动。服务启动后，可以通过与Claude对话的方式使用Sequential Thinking工具。

### 基本概念

Sequential Thinking工具允许AI助手以结构化、渐进的方式记录其思考过程，主要概念包括：

- **思考步骤(Thought)**：当前思考的具体内容
- **思考修订(Revision)**：对之前思考步骤的修正
- **思考分支(Branch)**：从某个点开始的替代思考路径
- **思考总数(Total Thoughts)**：估计完成任务所需的总思考步骤数

### 使用场景

Sequential Thinking特别适用于以下场景：

1. **复杂问题分解**：将复杂问题分解为可管理的步骤
2. **需要修订的规划和设计**：在规划过程中可能需要返回修改之前的思考
3. **可能需要修正的分析**：在分析过程中发现新信息时调整思路
4. **范围不明确的问题**：初始时无法确定问题的完整范围
5. **需要维持上下文的任务**：需要在多个步骤之间保持连贯性
6. **需要过滤无关信息的情境**：从大量信息中提炼出关键内容

### 常用案例

要使用Sequential Thinking，只需向Claude描述您的问题，并要求它使用分步思考方式解决。例如：

1. **复杂问题分析**
   ```
   请使用Sequential Thinking方法分析这个复杂的软件架构问题，一步步思考最佳解决方案。
   ```

2. **算法设计**
   ```
   请用Sequential Thinking方法设计一个高效的数据处理算法，考虑各种边界情况。
   ```

3. **决策制定**
   ```
   我面临A和B两个选择，请使用Sequential Thinking方法帮我分析利弊并做出决策。
   ```

## 参数说明

Sequential Thinking工具接受以下参数：

- `thought` (字符串)：当前思考步骤的内容
- `nextThoughtNeeded` (布尔值)：是否需要继续思考
- `thoughtNumber` (整数)：当前思考步骤的编号
- `totalThoughts` (整数)：估计完成任务所需的总思考步骤数
- `isRevision` (布尔值，可选)：是否是对先前思考的修订
- `revisesThought` (整数，可选)：正在重新考虑的思考步骤编号
- `branchFromThought` (整数，可选)：分支起点的思考步骤编号
- `branchId` (字符串，可选)：分支标识符
- `needsMoreThoughts` (布尔值，可选)：是否需要更多思考步骤

## 常见问题

### 思考步骤过多

**问题**：AI生成了过多的思考步骤，变得冗长。

**解决方案**：
- 在开始时明确要求更简洁的思考
- 在对话中提示AI合并相关步骤
- 设置较小的`totalThoughts`初始值

### 思考过程不够深入

**问题**：思考步骤过于表面，没有深入分析。

**解决方案**：
- 明确要求更深入的分析
- 提出具体问题引导思考
- 要求在每个步骤中考虑多个角度

### 无法修订早期思考

**问题**：AI似乎无法有效修订早期的思考步骤。

**解决方案**：
- 明确指出需要修订的步骤编号
- 提供新的信息或见解，解释为什么需要修订
- 使用`isRevision`和`revisesThought`参数

## 参考资源

- [Sequential Thinking MCP Server GitHub仓库](https://github.com/smithery-ai/reference-servers/tree/main/src/sequentialthinking)
- [Model Context Protocol (MCP) 规范](https://github.com/anthropics/anthropic-cookbook/tree/main/mcp)
- [Cursor IDE官方文档](https://cursor.sh/docs) 