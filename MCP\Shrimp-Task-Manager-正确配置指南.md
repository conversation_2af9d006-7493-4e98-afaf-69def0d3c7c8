# MCP Shrimp Task Manager 正确配置指南

## 概述
本指南提供了在 Augment Code 插件中正确配置 MCP Shrimp Task Manager 的步骤。

## 前提条件
- 已安装 Augment Code VS Code 插件
- 已获取 Smithery API 密钥：https://smithery.ai/account/api-keys

## 配置步骤

### 1. Smithery CLI 登录
首先需要登录 Smithery CLI：

```bash
npx -y @smithery/cli@latest login
```

输入您的 API 密钥：`5d023817-6b6e-4f0f-81a0-6f2bb2b11ac1`

### 2. 创建数据目录
```bash
mkdir "D:\Jolly\Shrimp"
```

### 3. 测试服务器运行
```bash
npx -y @smithery/cli@latest run cjo4m06/mcp-shrimp-task-manager --config "{\"dataDir\":\"D:\\Jolly\\Shrimp\"}"
```

### 4. Augment Code 配置

在 Augment Code 插件的 MCP 服务器配置中添加：

```json
{
  "mcpServers": {
    "mcp-shrimp-task-manager": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@smithery/cli@latest",
        "run",
        "cjo4m06/mcp-shrimp-task-manager",
        "--config",
        "{\"dataDir\":\"D:\\\\Jolly\\\\Shrimp\"}"
      ]
    }
  }
}
```

## 重要说明

### 正确的包名格式
- ✅ 正确：`cjo4m06/mcp-shrimp-task-manager`
- ❌ 错误：`@cjo4m06/mcp-shrimp-task-manager`

### 认证方式
- Smithery CLI 登录后会自动处理认证
- 不需要在命令行中传递 `--profile` 和 `--key` 参数

### JSON 配置格式
- 在 Windows 命令行中，JSON 字符串中的反斜杠需要双重转义
- 正确格式：`"{\"dataDir\":\"D:\\\\Jolly\\\\Shrimp\"}"`

## 验证配置

1. 重启 VS Code
2. 打开 Augment Code 插件
3. 检查 MCP 服务器连接状态
4. 确认可以看到 15 个 Shrimp Task Manager 工具

## 可用工具

MCP Shrimp Task Manager 提供以下 15 个工具：
1. view_tasklist - 查看当前任务列表
2. add_tasks - 添加新任务
3. update_tasks - 更新任务状态
4. reorganize_tasklist - 重组任务列表
5. 以及其他任务管理相关工具

## 故障排除

### 常见错误
1. **"too many arguments for 'run'"** - 检查包名格式，不要使用 @ 前缀
2. **"Error parsing config"** - 检查 JSON 格式，确保反斜杠正确转义
3. **认证失败** - 确保已通过 `smithery login` 登录

### 日志查看
如果遇到问题，可以查看详细日志：
```bash
npx -y @smithery/cli@latest run cjo4m06/mcp-shrimp-task-manager --config "{\"dataDir\":\"D:\\Jolly\\Shrimp\"}" --verbose
```

## 配置文件位置
- 配置文件：`D:\Jolly\Develop\Cursor-rules\augment-shrimp-config.json`
- 数据目录：`D:\Jolly\Shrimp`
- 测试脚本：`D:\Jolly\Develop\Cursor-rules\test-shrimp-config.bat`

## 更新日期
2025-01-02 - 修正了包名格式和认证方式的错误配置