# MCP Shrimp Task Manager 配置参数详解

## 📋 参数概览

```json
{
  "mcpServers": {
    "mcp-shrimp-task-manager": {
      "command": "npx",
      "args": ["mcp-shrimp-task-manager"],
      "env": {
        "SHRIMP_DATA_DIR": "D:\\Jolly\\Shrimp"
      },
      "cwd": "D:\\Jolly\\Develop\\Cursor-rules"
    }
  }
}
```

## 🔍 参数详细说明

### 1. `"cwd": "D:\\Jolly\\Develop\\Cursor-rules"`

#### 含义
- **CWD** = Current Working Directory（当前工作目录）
- 指定 MCP 服务器进程启动时的工作目录

#### 作用
- **包查找**：`npx` 命令会在这个目录中查找 `mcp-shrimp-task-manager` 包
- **相对路径解析**：所有相对路径都会基于这个目录解析
- **npm 上下文**：确保在正确的 npm 项目环境中运行

#### 为什么是这个路径
```
D:\Jolly\Develop\Cursor-rules\
├── package.json              ← npm 项目配置
├── node_modules\              ← 安装的包目录
│   └── mcp-shrimp-task-manager\  ← 我们安装的包
└── package-lock.json         ← 依赖锁定文件
```

#### 如果修改这个路径
- ✅ **可以修改**：如果你在其他目录安装了包
- ⚠️ **注意**：必须确保目标目录包含 `mcp-shrimp-task-manager` 包
- 🔧 **示例**：如果你在 `C:\MyProject` 安装了包，就改为 `"cwd": "C:\\MyProject"`

---

### 2. `"SHRIMP_DATA_DIR": "D:\\Jolly\\Shrimp"`

#### 含义
- **DATA_DIR** = 数据目录
- Shrimp Task Manager 存储所有数据的位置

#### 存储内容
根据源码分析，这个目录会存储：

1. **任务数据**
   - 任务列表和状态
   - 任务依赖关系
   - 任务执行历史

2. **项目信息**
   - 项目规则和标准
   - 代码规范配置
   - 项目元数据

3. **对话历史**
   - AI 对话记录
   - 任务分析过程
   - 决策历史

4. **备份文件**
   - 任务历史备份
   - 配置文件备份

5. **Web GUI 文件**（如果启用）
   - `WebGUI.md` - 包含访问地址
   - Web 界面相关文件

#### 目录结构示例
```
D:\Jolly\Shrimp\
├── tasks\                    # 任务数据
│   ├── current-tasks.json
│   ├── completed-tasks.json
│   └── task-history\
├── projects\                 # 项目配置
│   ├── rules.md
│   └── standards.json
├── conversations\            # 对话历史
├── backups\                 # 备份文件
└── WebGUI.md               # Web 界面访问信息（如果启用）
```

#### 如果修改这个路径
- ✅ **完全可以修改**：这是用户数据，可以放在任何位置
- 💡 **建议位置**：
  - `D:\MyProjects\TaskData` - 项目相关
  - `C:\Users\<USER>\Documents\ShrimpTasks` - 用户文档
  - `E:\WorkData\Tasks` - 工作数据盘

#### 环境变量优先级
根据源码，数据目录的确定顺序：
1. `SHRIMP_DATA_DIR` 环境变量（我们设置的）
2. `DATA_DIR` 环境变量
3. 项目根目录 + `/data`

---

## 🔧 配置示例

### 示例1：标准配置
```json
{
  "cwd": "D:\\Jolly\\Develop\\Cursor-rules",
  "env": {
    "SHRIMP_DATA_DIR": "D:\\Jolly\\Shrimp"
  }
}
```

### 示例2：自定义项目目录
```json
{
  "cwd": "C:\\MyProjects\\AITools",
  "env": {
    "SHRIMP_DATA_DIR": "C:\\MyProjects\\TaskData"
  }
}
```

### 示例3：用户文档目录
```json
{
  "cwd": "D:\\Jolly\\Develop\\Cursor-rules",
  "env": {
    "SHRIMP_DATA_DIR": "C:\\Users\\<USER>\\Documents\\ShrimpTasks"
  }
}
```

## ⚠️ 重要注意事项

### CWD 路径要求
1. **必须存在** `package.json` 文件
2. **必须安装** `mcp-shrimp-task-manager` 包
3. **路径格式**：Windows 使用双反斜杠 `\\`

### DATA_DIR 路径要求
1. **目录权限**：确保可读写
2. **磁盘空间**：预留足够空间存储任务数据
3. **备份考虑**：重要数据建议定期备份

### 路径格式注意
- ✅ 正确：`"D:\\Jolly\\Shrimp"`
- ❌ 错误：`"D:\Jolly\Shrimp"` （单反斜杠会被转义）
- ✅ 替代：`"D:/Jolly/Shrimp"` （正斜杠也可以）

## 🚀 快速测试

### 测试 CWD 配置
```bash
cd "D:\Jolly\Develop\Cursor-rules"
npx mcp-shrimp-task-manager --help
```

### 测试 DATA_DIR 配置
```bash
$env:SHRIMP_DATA_DIR="D:\Jolly\Shrimp"
npx mcp-shrimp-task-manager
# 检查 D:\Jolly\Shrimp 目录是否创建了文件
```

---

**总结**：
- `cwd` = 程序在哪里运行（包的位置）
- `SHRIMP_DATA_DIR` = 数据存储在哪里（你的任务数据）