{"name": "BrowserTools MCP", "version": "1.2.0", "description": "MCP tool for AI code editors to capture data from a browser such as console logs, network requests, screenshots and more", "manifest_version": 3, "devtools_page": "devtools.html", "permissions": ["activeTab", "debugger", "storage", "tabs", "tabCapture", "windows"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}}